<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iL的个人视觉小说评分榜</title>
    <meta name="description" content="iL的个人视觉小说评分榜">
    <meta name="keywords" content="视觉小说, 评分, 推荐">
    <meta name="author" content="iL">
    <link rel="icon" href="favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://api.illusionlie.com" crossorigin>
    <link rel="preload" href="/css/style.css" as="style">
    <link rel="preload" href="/js/main.js" as="script">
    <link rel="preload" href="/main.json" as="fetch" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>

    <div class="background-overlay"></div>

    <!-- 顶部 Banner 和导航 -->
    <header class="main-header">
        <div class="banner">
            <h1 class="banner-title">My VN Shelf</h1>
            <!-- Wrapped all right-side actions in a container -->
            <div class="header-actions">
                <nav class="banner-nav desktop-only">
                    <a href="https://illusionlie.com" target="_blank">关于我</a>
                    <a href="https://github.com/illusionlie/my-vn-shelf" target="_blank">GitHub</a>
                </nav>

                <!-- Container for mobile-specific "More" menu -->
                <div class="mobile-nav mobile-only">
                    <button id="more-menu-toggle" class="more-menu-toggle-btn" aria-label="Toggle more options">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3C10.9 3 10 3.9 10 5C10 6.1 10.9 7 12 7C13.1 7 14 6.1 14 5C14 3.9 13.1 3 12 3ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM12 17C10.9 17 10 17.9 10 19C10 20.1 10.9 21 12 21C13.1 21 14 20.1 14 19C14 17.9 13.1 17 12 17Z"/></svg>
                    </button>
                    <div id="more-menu" class="more-menu">
                        <a href="https://illusionlie.com" target="_blank">关于我</a>
                        <a href="https://github.com/illusionlie/my-vn-shelf" target="_blank">GitHub</a>
                    </div>
                </div>

                <button id="theme-toggle-btn" class="theme-toggle-btn" aria-label="Toggle color scheme">
                    <!-- SVG icon is dynamically inserted by JS -->
                </button>
            </div>
        </div>
        <div class="controls-bar">
            <button id="drawer-toggle" class="drawer-toggle-btn" aria-label="Toggle Categories">
                <div class="hamburger-icon">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>
            <div class="search-container">
                <input type="search" id="search-input" placeholder="搜索游戏名...">
            </div>
        </div>
    </header>

    <!-- 分类抽屉 -->
    <aside id="category-drawer" class="category-drawer">
        <h2 class="drawer-title">分类筛选</h2>
        <div id="category-list" class="category-list">
            <!-- 分类将由JS动态生成 -->
        </div>
    </aside>

    <!-- 主内容区：VN列表 -->
    <main id="vn-list-container" class="vn-list-container">
        <!-- 卡片将由JS动态生成 -->
    </main>

    <!-- 弹窗 Modal -->
    <div id="modal-container" class="modal-container" style="display: none;">
        <div class="modal-content">
            <button id="modal-close-btn" class="modal-close-btn" aria-label="Close Modal">
                <!-- SVG Icon for close -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 10.586l4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.414 1.414-4.95-4.95-4.95 4.95-1.414-1.414 4.95-4.95-4.95-4.95L7.05 5.636l4.95 4.95z"></path></svg>
            </button>
            <div id="modal-body">
                <!-- 弹窗内容将由JS动态生成 -->
            </div>
        </div>
    </div>

    <script src="js/main.js?version=0.3.0"></script>
</body>
</html>