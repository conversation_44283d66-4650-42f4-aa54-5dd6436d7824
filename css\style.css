/* --- CSS Variables and Base Setup --- */
:root {
    /* --- Base --- */
    --font-family-base: 'Noto Sans SC', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    --accent-color: #007AFF;
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --transition-fast: 0.2s ease-in-out;
    --transition-medium: 0.3s ease-in-out;
    
    /* --- Dark Mode (Default) --- */
    --primary-text-color: #E2E2E2;
    --secondary-text-color: #A0A0A0;
    --body-bg-color: #121212;
    --bg-overlay-color: rgba(0, 0, 0, 0.2);
    --header-bg-color: rgba(28, 28, 30, 0.75);
    --card-bg-color: rgba(30, 30, 30, 0.65);
    --drawer-bg-color: rgba(20, 20, 20, 0.8);
    --modal-bg-color: rgba(44, 44, 46, 0.75);
    --overlay-bg-color: rgba(0, 0, 0, 0.6);
    --input-bg-color: rgba(255, 255, 255, 0.1);
    --input-focus-bg-color: rgba(255, 255, 255, 0.15);
    --tag-bg-color: rgba(255, 255, 255, 0.1);
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-light: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.2);
    --star-empty-color: #555;
}

body.light-mode {
    /* --- Light Mode Overrides --- */
    --primary-text-color: #1d1d1f;
    --secondary-text-color: #6e6e73;
    --body-bg-color: #f5f5f7;
    --bg-overlay-color: rgba(255, 255, 255, 0.05);
    --header-bg-color: rgba(255, 255, 255, 0.6);
    --card-bg-color: rgba(255, 255, 255, 0.65);
    --drawer-bg-color: rgba(245, 245, 247, 0.75);
    --modal-bg-color: rgba(242, 242, 247, 0.8);
    --overlay-bg-color: rgba(0, 0, 0, 0.4);
    --input-bg-color: rgba(0, 0, 0, 0.05);
    --input-focus-bg-color: rgba(0, 0, 0, 0.08);
    --tag-bg-color: rgba(0, 0, 0, 0.05);
    --border-color: rgba(0, 0, 0, 0.1);
    --shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
    --star-empty-color: #ccc;
}

body {
    font-family: var(--font-family-base);
    background-color: var(--body-bg-color);
    background-image: url('https://api.illusionlie.com/randomBackground'); /* 可自定义背景 */
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--primary-text-color);
    margin: 0;
    padding-top: 120px; /* 为固定的 header 留出空间 */
    transition: padding-left var(--transition-medium), color var(--transition-medium), background-color var(--transition-medium); /* 添加颜色过渡 */
}

body.drawer-open {
    padding-left: 260px;
    overflow: hidden;
}

.background-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-overlay-color);
    z-index: -1;
    transition: background-color var(--transition-medium);
    /* Global subtle blur for the entire background image */
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* --- Header --- */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: var(--header-bg-color);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-bottom: 1px solid var(--border-color);
    padding: 10px 5%;
    transition: left var(--transition-medium);
}

.banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.banner-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.banner-nav {
    display: flex;
    align-items: center;
    gap: 20px; /* 元素间距 */
}

.banner-nav a {
    color: var(--secondary-text-color);
    text-decoration: none;
    transition: color var(--transition-fast);
    margin-left: 0;
}

.banner-nav a:hover {
    color: var(--accent-color);
}

/* --- Mobile Navigation Styles --- */
.mobile-nav {
    position: relative; /* For positioning the dropdown menu */
}

.more-menu-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: var(--secondary-text-color);
    display: flex;
    align-items: center;
    justify-content: center;
}
.more-menu-toggle-btn:hover {
    color: var(--accent-color);
}
.more-menu-toggle-btn svg {
    width: 22px;
    height: 22px;
}

.more-menu {
    position: absolute;
    top: calc(100% + 10px); /* Position below the button with a small gap */
    right: 0;
    background-color: var(--drawer-bg-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    width: 150px;
    padding: 8px;
    z-index: 110; /* Above the header */
    
    /* Hidden state */
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
    transition: opacity var(--transition-fast), transform var(--transition-fast);
}

.more-menu.open {
    /* Visible state */
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.more-menu a {
    display: block;
    color: var(--primary-text-color);
    text-decoration: none;
    padding: 10px 12px;
    border-radius: 6px;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.more-menu a:hover {
    background-color: var(--accent-color);
    color: #fff;
}

/* --- Hamburger Icon Animation --- */
.drawer-toggle-btn {
    background: none;
    border: none;
    color: var(--primary-text-color);
    cursor: pointer;
    padding: 10px;
    z-index: 10; /* Ensure it's clickable */
    position: relative;
}

.hamburger-icon {
    width: 24px;
    height: 18px;
    position: relative;
    transform: rotate(0deg);
    transition: .5s ease-in-out;
}

.hamburger-icon span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--primary-text-color);
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

/* Position the three lines */
.hamburger-icon span:nth-child(1) {
    top: 0px;
}

.hamburger-icon span:nth-child(2) {
    top: 8px;
}

.hamburger-icon span:nth-child(3) {
    top: 16px;
}

/* Animation when drawer is open */
.drawer-open .hamburger-icon span:nth-child(1) {
    top: 8px;
    transform: rotate(135deg); /* 45 degrees + 90 degrees */
}

.drawer-open .hamburger-icon span:nth-child(2) {
    opacity: 0;
    transform: translateX(-20px); /* Fade out and slide left */
}

.drawer-open .hamburger-icon span:nth-child(3) {
    top: 8px;
    transform: rotate(-135deg); /* -45 degrees - 90 degrees */
}

.controls-bar {
    display: flex;
    align-items: center;
    gap: 15px;
}

.theme-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: var(--secondary-text-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle-btn:hover {
    color: var(--accent-color);
}

.theme-toggle-btn svg {
    width: 20px;
    height: 20px;
}

.drawer-toggle-btn {
    background: none;
    border: none;
    color: var(--primary-text-color);
    cursor: pointer;
    padding: 5px;
}
.drawer-toggle-btn svg { width: 24px; height: 24px; }

.search-container {
    flex-grow: 1;
}

#search-input {
    width: 100%;
    padding: 8px 15px;
    background-color: var(--input-bg-color);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    color: var(--primary-text-color);
    font-size: 1rem;
    outline: none;
    transition: all var(--transition-fast);
}

#search-input:focus {
    background-color: var(--input-focus-bg-color);
    border-color: var(--accent-color);
}

/* --- Style for scrollbar to match theme --- */
/* Works on WebKit browsers like Chrome, Safari, Edge */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--tag-bg-color);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-color);
}

/* --- Category Drawer --- */
.category-drawer {
    position: fixed;
    top: 0;
    left: 0;
    width: 260px;
    height: 100vh;
    background: var(--drawer-bg-color);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    border-right: 1px solid var(--border-color);
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
    z-index: 200;
    padding: 20px;
    padding-top: 140px; /* 避开 header */
    box-sizing: border-box;
    display: flex; /* Use flexbox for better vertical layout control */
    flex-direction: column;
}

.category-drawer.open {
    transform: translateX(0);
}

.drawer-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 20px;
    color: var(--secondary-text-color);
    flex-shrink: 0; /* Prevent title from shrinking */
}

.category-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: auto; /* Show vertical scrollbar only when needed */
    flex-grow: 1; /* Allow the list to take up all available vertical space */
    padding-right: 5px; /* Add a little space for the scrollbar */
}

.category-btn {
    background-color: var(--tag-bg-color);
    color: var(--primary-text-color);
    border: 1px solid transparent;
    padding: 8px 15px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.category-btn:hover {
    background-color: var(--accent-color);
    color: white;
}
.category-btn.active {
    background-color: var(--accent-color);
    color: white;
    font-weight: 500;
}

/* --- VN List & Cards --- */
.vn-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    padding: 0 5%;
    transition: opacity var(--transition-medium);
}

.vn-cover-container {
    position: relative;
    display: inline-block;
    line-height: 0;
}

.vn-cover-container img {
    max-width: 100%; /* 确保图片响应式 */
    display: block;
}

.vn-cover-badge {
    position: absolute;
    top: 16px;
    left: 15px;
    background-color: #33e42ac1;
    color: rgba(255, 255, 255, 0.845);
    padding: 14px 9px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
    z-index: 1;
}

.vn-card {
    background: var(--card-bg-color);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
}

.vn-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-medium);
}

.vn-card-cover {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.vn-card-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.vn-title-link {
    color: inherit;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.vn-title-link:hover {
    color: var(--accent-color);
}

.vn-title-translated {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.vn-title-original {
    font-size: 0.85rem;
    color: var(--secondary-text-color);
    margin-top: 4px;
}

.vn-company {
    font-size: 0.9rem;
    color: var(--secondary-text-color);
    margin: 12px 0;
}

.vn-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 12px 0;
}

.vn-tag {
    background-color: var(--tag-bg-color);
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.vn-card-footer {
    margin-top: auto; /* Push footer to the bottom */
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.vn-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.stars-container {
    display: flex;
    gap: 2px;
}
.star { color: #FFD700; }
.star.empty { color: var(--star-empty-color); }

.rating-score {
    font-size: 1.1rem;
    font-weight: 500;
}

.time-length {
    font-size: 0.8rem;
    color: var(--secondary-text-color);
    margin-left: 10px;
}

.vn-review-short {
    margin-top: 15px;
    font-size: 0.9rem;
    color: var(--secondary-text-color);
    line-height: 1.6;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.vn-review-short:hover {
    color: var(--primary-text-color);
}

/* --- Modal --- */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--overlay-bg-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity var(--transition-medium);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    pointer-events: none;
}
.modal-container.visible {
    opacity: 1;
    pointer-events: auto;
}

.modal-content {
    background: var(--modal-bg-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-medium);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 30px;
    position: relative;
    transform: scale(0.95);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--border-color);
    transition: transform var(--transition-medium), background-color var(--transition-medium);
}
.modal-container.visible .modal-content {
    transform: scale(1);
}

.modal-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: var(--secondary-text-color);
    cursor: pointer;
    padding: 5px;
}
.modal-close-btn svg { width: 28px; height: 28px; }
.modal-close-btn:hover { color: var(--primary-text-color); }

/* Modal Content Styles */
.modal-rating-details h3, .modal-review-full h3 {
    margin-top: 0;
    font-size: 1.5rem;
    margin-bottom: 25px; /* Added space below title */
}
.rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}
.rating-item:last-child { border-bottom: none; }
.rating-label { font-size: 1rem; }
.rating-value { font-size: 1.1rem; font-weight: 500; color: var(--accent-color); }

/* Container for the parsed review content */
.review-content-body {
    display: flex;
    flex-direction: column;
    gap: 12px; /* Consistent spacing between all elements */
}
.review-full-text {
    font-size: 1rem;
    line-height: 1.8;
    color: var(--secondary-text-color);
    margin: 0; /* Margin is now handled by the gap in the container */
    white-space: normal; /* Override pre-wrap as we now have proper paragraphs */
}

/* Styles for the new elements */
.review-separator {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: 10px 0; /* Vertical spacing */
}

.review-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
    margin: 8px 0; /* Vertical spacing */
}

/* --- Responsive Design --- */
.mobile-only {
    display: none;
}

@media screen and (max-width: 700px) {
    body {
        background-image: url('https://api.illusionlie.com/randomBackground?type=vertical');
    }
    body.drawer-open {
        padding-left: 260px;
    }
    .main-header {
        padding: 10px 15px;
    }
    .main-header.drawer-open {
        left: 260px;
    }
    .vn-list-container {
        padding: 0 15px;
    }
    .banner-title { font-size: 1.2rem; }
    .banner-nav { display: none; } /* Hide nav on small screens */
    .desktop-only {
        display: none;
    }
    .mobile-only {
        display: block; /* Or flex, depending on needs */
    }
}
@media screen and (max-width: 500px) {
    body {
        background-image: url('https://api.illusionlie.com/randomBackground?type=vertical');
    }
    body.drawer-open {
        padding-left: 0; /* No content shift on mobile */
    }
    .category-drawer {
        width: 85%;
        max-width: 300px;
    }
    .main-header.drawer-open {
        left: 0; /* Header does not shift */
    }
    .vn-card-cover {
        height: 320px;
    }
}